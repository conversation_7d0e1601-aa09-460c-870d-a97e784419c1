<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns:tx="http://www.springframework.org/schema/tx"
	   xmlns:context="http://www.springframework.org/schema/context"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-4.1.xsd
       http://www.springframework.org/schema/tx
       http://www.springframework.org/schema/tx/spring-tx-4.1.xsd
       http://www.springframework.org/schema/context 
	   http://www.springframework.org/schema/context/spring-context-4.1.xsd">


	<context:annotation-config />
	<context:component-scan base-package="net.fuzui.StudentInfo.service" />

	
	<!-- druid连接池 -->
	<bean id="abstractDataSource" abstract="true" class="com.alibaba.druid.pool.DruidDataSource" init-method="init" destroy-method="close">
		<property name="driverClassName" value="com.mysql.cj.jdbc.Driver" />
		<!-- 配置初始化大小、最大、最小 -->
		<property name="initialSize" value="1" />
		<property name="minIdle" value="10" />
		<property name="maxActive" value="10" />
		
		<!-- 配置获取连接等待超时的实际 -->
		<property name="maxWait" value="60000" />
	</bean>
	
	
	<!-- 配置写库 继承abstractDataSource-->
	<bean id="dataSourceWrite" parent="abstractDataSource">
		<!-- 基本url、username、password -->
		<property name="url" value="******************************************************************************************************************" />
		<property name="username" value="root" />
		<property name="password" value="root" />
	</bean>
	
	<!-- 配置读库 继承abstractDataSource-->
	<bean id="dataSourceRead" parent="abstractDataSource">
		<!-- 基本 url、username、password -->
		<property name="url" value="******************************************************************************************************************" />
		<property name="username" value="root" />
		<property name="password" value="root" />
	</bean>
	
	<!-- 动态数据源 -->
	<bean id="dataSource" class="net.fuzui.StudentInfo.mysql_rws.DynamicDataSource">
		<property name="writeDataSource" ref="dataSourceWrite" />
		<property name="readDataSource" ref="dataSourceRead" />
	</bean>
	<!--
	<tx:annotation-driven transaction-manager="transactionManager" />
	-->
	<!-- sqlSessionFactory -->
	<bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
		<!-- 实例化sqlSessionFaction时用到上面的数据源以及sql映射文件 -->
		<property name="dataSource" ref="dataSource" />
		<!-- 引入mybatis配置文件 -->
		<property name="configLocation" value="classpath:config/mybatis/mybatis-config.xml" />
		<!-- mapper配置文件 -->
		<property name="mapperLocations" value="classpath:mapper/*.xml" />
		<!-- pojo -->
		<property name="typeAliasesPackage" value="net.fuzui.StudentInfo.pojo" />
		<!-- plugin -->
		<property name="plugins">
			<array>
				<bean class="net.fuzui.StudentInfo.mysql_rws.DynamicPlugin" />
			</array>
		</property>			
	</bean>
	
	<!-- 配置扫描器 -->
	<bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
		<property name="basePackage" value="net.fuzui.StudentInfo.mapper"></property>
		<property name="sqlSessionFactoryBeanName" value="sqlSessionFactory"></property>
	</bean>
	
</beans>
