/*
 Navicat Premium Data Transfer

 Source Server         : a
 Source Server Type    : MySQL
 Source Server Version : 50729
 Source Host           : localhost:3306
 Source Schema         : ssm_student_info

 Target Server Type    : MySQL
 Target Server Version : 50729
 File Encoding         : 65001

 Date: 19/06/2022 16:13:19
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for admin
-- ----------------------------
DROP TABLE IF EXISTS `admin`;
CREATE TABLE `admin`  (
  `Aname` varchar(12) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `Apassword` varchar(12) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`Aname`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Compact;

-- ----------------------------
-- Records of admin
-- ----------------------------
INSERT INTO `admin` VALUES ('admin', '123456');

-- ----------------------------
-- Table structure for course
-- ----------------------------
DROP TABLE IF EXISTS `course`;
CREATE TABLE `course`  (
  `Cid` char(4) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `Cname` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `Cintroduction` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Type` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `belongcoll` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `belongpro` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`Cid`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Compact;

-- ----------------------------
-- Records of course
-- ----------------------------
INSERT INTO `course` VALUES ('0011', 'java程序设计', '1111', '必修', '计算机学院', '网络工程');
INSERT INTO `course` VALUES ('1005', '大学英语5', '大学英语5', '必修', '外国语学院', '外国语所有专业');
INSERT INTO `course` VALUES ('12', '最代码课程ddd', '培训如何下载运行zuidaima.com的代码', '必修', '计算机学院', '软件工程');
INSERT INTO `course` VALUES ('123', '123', '123', '必修', '计算机学院', '网络工程');
INSERT INTO `course` VALUES ('2002', '网络工程', '网络安全、网络路由配置', '必修', '计算机学院', '网络工程');
INSERT INTO `course` VALUES ('2003', 'C语言基础', 'C语言介绍使用', '必修', '计算机学院', '所有');
INSERT INTO `course` VALUES ('2004', '大学英语（1）', '大学英语', '必修', '所有', '所有');
INSERT INTO `course` VALUES ('2005', 'c++基础', 'c++基础', '必修', '计算机学院', '网络工程');
INSERT INTO `course` VALUES ('5002', 'javaEE', 'java相关框架', '选修', '计算机学院', '软件工程');

-- ----------------------------
-- Table structure for coursecomment
-- ----------------------------
DROP TABLE IF EXISTS `coursecomment`;
CREATE TABLE `coursecomment`  (
  `ccid` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `cid` varchar(12) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `sid` varchar(12) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `content` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `score` int(11) NULL DEFAULT NULL,
  `ctime` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`ccid`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '课程评价表' ROW_FORMAT = Compact;

-- ----------------------------
-- Records of coursecomment
-- ----------------------------

-- ----------------------------
-- Table structure for courseplan
-- ----------------------------
DROP TABLE IF EXISTS `courseplan`;
CREATE TABLE `courseplan`  (
  `Courseclass` varchar(12) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `coursetime` varchar(12) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `courseweek` varchar(12) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `cid` char(4) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `tid` char(4) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `classroom` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `credits` int(11) NULL DEFAULT NULL,
  `period` int(11) NULL DEFAULT NULL,
  `Totalnum` int(11) NULL DEFAULT NULL,
  PRIMARY KEY (`Courseclass`) USING BTREE,
  INDEX `cid`(`cid`) USING BTREE,
  INDEX `tid`(`tid`) USING BTREE,
  CONSTRAINT `courseplan_ibfk_1` FOREIGN KEY (`cid`) REFERENCES `course` (`Cid`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `courseplan_ibfk_2` FOREIGN KEY (`tid`) REFERENCES `teacher` (`Tid`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Compact;

-- ----------------------------
-- Records of courseplan
-- ----------------------------
INSERT INTO `courseplan` VALUES ('123', '12', '1', '0011', '0005', '500', 3, 3, 50);
INSERT INTO `courseplan` VALUES ('456', '12', '1', '1005', '0005', '500', 3, 3, 50);
INSERT INTO `courseplan` VALUES ('C语言1班', '12', '7', '2003', '0002', '30802', 4, 40, 30);
INSERT INTO `courseplan` VALUES ('javaE2', '12,34', '3', '5002', '0002', '10105', 5, 25, 30);
INSERT INTO `courseplan` VALUES ('网络班级2', '34,56', '4', '2002', '0002', '20202', 5, 25, 35);

-- ----------------------------
-- Table structure for grade
-- ----------------------------
DROP TABLE IF EXISTS `grade`;
CREATE TABLE `grade`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sid` char(12) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `cid` char(4) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `grade` int(3) NULL DEFAULT NULL,
  `credits` int(11) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Compact;

-- ----------------------------
-- Records of grade
-- ----------------------------
INSERT INTO `grade` VALUES (3, '201507021225', '5002', 75, 5);
INSERT INTO `grade` VALUES (5, '201507024129', '5002', 79, 5);
INSERT INTO `grade` VALUES (6, '201507024125', '5002', 76, 5);
INSERT INTO `grade` VALUES (7, '201507024126', '5002', 79, 5);
INSERT INTO `grade` VALUES (8, '201507024128', '5002', 58, NULL);
INSERT INTO `grade` VALUES (12, '201507024128', '5002', 60, 5);
INSERT INTO `grade` VALUES (13, '201507021227', '2002', 22, NULL);
INSERT INTO `grade` VALUES (14, '201507021227', '2002', 66, 5);

-- ----------------------------
-- Table structure for sc
-- ----------------------------
DROP TABLE IF EXISTS `sc`;
CREATE TABLE `sc`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `cid` char(4) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `sid` char(12) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `cid`(`cid`) USING BTREE,
  INDEX `sid`(`sid`) USING BTREE,
  CONSTRAINT `sc_ibfk_1` FOREIGN KEY (`cid`) REFERENCES `course` (`Cid`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `sc_ibfk_2` FOREIGN KEY (`sid`) REFERENCES `student` (`Sid`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 86 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Compact;

-- ----------------------------
-- Records of sc
-- ----------------------------
INSERT INTO `sc` VALUES (84, '1005', '201507024125');
INSERT INTO `sc` VALUES (85, '2002', '201507021227');

-- ----------------------------
-- Table structure for student
-- ----------------------------
DROP TABLE IF EXISTS `student`;
CREATE TABLE `student`  (
  `Sid` char(12) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `Sname` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `Sidcard` char(18) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `Ssex` varchar(18) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Spassword` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `Sage` char(2) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `Classr` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `profession` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `college` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`Sid`) USING BTREE,
  INDEX `class`(`Classr`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Compact;

-- ----------------------------
-- Records of student
-- ----------------------------
INSERT INTO `student` VALUES ('201507021227', '依凡', '3443778845551214', '女', '000000', '21', '软件B151', '软件工程', '计算机学院');
INSERT INTO `student` VALUES ('201507023225', '苏通', '1405819961012621', '男', '000000', '22', '软件B152', '软件工程', '计算机学院');
INSERT INTO `student` VALUES ('201507023226', '赵玉', '1253333388877788', '女', '000000', '23', '软件B152', '软件工程', '计算机学院');
INSERT INTO `student` VALUES ('201507023228', '张亚', '8885456456498512', '女', '000000', '21', '软件B152', '软件工程', '计算机学院');
INSERT INTO `student` VALUES ('201507023229', '王野', '1525659875656223', '男', '000000', '24', '网络B151', '网络工程', '计算机学院');
INSERT INTO `student` VALUES ('201507023230', '欧阳离镜', '7849656654641545', '男', '000000', '22', '软件B152', '软件工程', '计算机学院');
INSERT INTO `student` VALUES ('201507024125', '王泽', '1405819961012621', '男', '', '22', '网络B151', '网络工程', '计算机学院');
INSERT INTO `student` VALUES ('201507024126', '高猛', '1253333388877788', '男', '00000', '23', '网络B151', '网络工程', '计算机学院');
INSERT INTO `student` VALUES ('201507024128', '李甜', '8885456456498512', '女', '000000', '21', '网络B151', '网络工程', '计算机学院');
INSERT INTO `student` VALUES ('201507024129', '宋球', '1525659875656223', '男', '000000', '24', '网络B151', '网络工程', '计算机学院');
INSERT INTO `student` VALUES ('201507024130', '张敏', '7849656654641545', '男', '00000', '22', '网络B151', '网络工程', '计算机学院');
INSERT INTO `student` VALUES ('201507024227', '丁晨', '5564778845551214', '女', '000000', '23', '软件B152', '软件工程', '计算机学院');
INSERT INTO `student` VALUES ('2018', 'wang', '123456', '男', '123456', '18', '1', '软件工程', '计科院');
INSERT INTO `student` VALUES ('88888888', 'wu', '123456', '男', '123123', '18', '阿萨大', '阿萨大大', '阿瑟东撒的');

-- ----------------------------
-- Table structure for teacher
-- ----------------------------
DROP TABLE IF EXISTS `teacher`;
CREATE TABLE `teacher`  (
  `Tid` char(4) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `Tname` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `Tpassword` varchar(12) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `Tsex` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Introduction` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`Tid`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Compact;

-- ----------------------------
-- Records of teacher
-- ----------------------------
INSERT INTO `teacher` VALUES ('0002', '李浩', '111111', '男', 'kindly\r\nkindly');
INSERT INTO `teacher` VALUES ('0005', '王智宏', '123123', '男', '');
INSERT INTO `teacher` VALUES ('0022', '鞠红军', '111111', '男', 'kingdy');
INSERT INTO `teacher` VALUES ('1003', '任雨', '123456', '女', '随和！~');
INSERT INTO `teacher` VALUES ('1010', '', '000000', '男', 'zuidaima.com官方');
INSERT INTO `teacher` VALUES ('11', '最代码', '111111', '男', 'zuidaima.com官方');
INSERT INTO `teacher` VALUES ('5010', '冬玲', '111111', '女', '和蔼可亲');
INSERT INTO `teacher` VALUES ('9005', '李梅', '654321', '男', '严厉！');

SET FOREIGN_KEY_CHECKS = 1;
