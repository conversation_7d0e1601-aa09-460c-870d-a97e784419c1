<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="net.fuzui.StudentInfo.mapper.TeacherMapper">

    <!-- 为了返回List，类型而定义的resultMap -->
    <resultMap type="Teacher" id="resultListTeacher">
        <result column="tid" property="tid" />
        <result column="tname" property="tname" />
        <result column="tpassword" property="tpassword" />
        <result column="tsex" property="tsex" />
        <result column="introduction" property="introduction" />
    </resultMap>





    <!-- 添加教师信息 -->
    <insert id="insertTeacher" parameterType="Teacher">
		insert into teacher(tid,tname,tpassword,tsex,introduction)
		value (#{tid}, #{tname},#{tpassword},#{tsex},#{introduction})
	</insert>

    <!-- 根据教师编号删除教师信息 -->
    <delete id="deleteTeacher" parameterType="String">
		delete from teacher where tid=#{tid}
	</delete>

    <!-- 根据教师编号修改教师信息 -->
    <update id="modifyTeacher" parameterType="Teacher">
		update teacher set
		tname=#{tname},tpassword=#{tpassword},tsex=#{tsex},introduction=#{introduction}
		where tid =#{tid}
	</update>

    <!-- 修改教师密码 -->
    <update id="modifyTeacherPwd">
		update teacher set tpassword=#{tpassword} where tid =#{tid}
	</update>

    <!-- 根据教师编号查询出教师实体 -->
    <select id="getByTeaTid" parameterType="String" resultType="net.fuzui.StudentInfo.pojo.Teacher">
		SELECT * from teacher where tid = #{tid}
	</select>

    <!-- 教师登录设置 -->
    <select id="queryByNamePwd" resultType="java.lang.String">
		select * from teacher where tid = #{tid} and tpassword= #{tpassword}
	</select>

    <!-- 查询全部教师 -->
    <select id="selectTeacherBySql" parameterType="map" resultMap="resultListTeacher">
		select * from teacher <!-- limit #{pageNo} , #{pageSize} -->
	</select>

    <!-- 根据教师编号查询学生信息 -->
    <select id="getByTeacherTid" parameterType="map" resultMap="resultListTeacher">
		select * from teacher where tid = #{tid} <!-- limit #{pageNo} , #{pageSize} -->
	</select>

    <!-- ajax验证教师编号是否存在 -->
    <select id="ajaxQueryByTid" resultType="java.lang.String">
		select tid from teacher where tid = #{tid}
	</select>

</mapper>