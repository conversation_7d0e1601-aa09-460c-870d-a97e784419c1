<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns:tx="http://www.springframework.org/schema/tx"
	   xmlns:context="http://www.springframework.org/schema/context"
	   xmlns:mvc="http://www.springframework.org/schema/mvc"
	   xsi:schemaLocation="http://www.springframework.org/schema/jdbc http://www.springframework.org/schema/jdbc/spring-jdbc-4.0.xsd
		http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc-4.0.xsd
		http://mybatis.org/schema/mybatis-spring http://mybatis.org/schema/mybatis-spring-1.2.xsd
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.1.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-4.1.xsd
		http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-4.0.xsd
		http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-4.1.xsd">


	<context:component-scan base-package="net.fuzui"></context:component-scan>
	<bean class="org.springframework.web.servlet.view.InternalResourceViewResolver">
		<property name="prefix" value="/"></property>
		<property name="suffix" value=".jsp"></property>
	</bean>


	<mvc:default-servlet-handler/>
	<mvc:annotation-driven />

	<mvc:interceptors>
        <mvc:interceptor>
        	<!-- 管理员登录拦截 -->
            <!-- 拦截所有URL中包含/AdminHandler/的请求 -->
            <mvc:mapping path="/AdminHandler/**"/>
            <bean class="net.fuzui.StudentInfo.interceptor.LoginInterceptor"></bean>
        </mvc:interceptor>
        <mvc:interceptor>
        	<!-- 管理员登录拦截 -->
            <!-- 拦截所有URL中包含CourseHandler/的请求 -->
            <mvc:mapping path="/CourseHandler/**"/>
            <bean class="net.fuzui.StudentInfo.interceptor.LoginInterceptor"></bean>
        </mvc:interceptor>
        <mvc:interceptor>
        	<!-- 管理员登录拦截 -->
            <!-- 拦截所有URL中包含CourseHandler/的请求 -->
            <mvc:mapping path="/admin/**"/>
            <bean class="net.fuzui.StudentInfo.interceptor.LoginInterceptor"></bean>
        </mvc:interceptor>
        <mvc:interceptor>
        	<!-- 学生登录拦截 -->
            <!-- 拦截所有URL中包含/StudentHandler/的请求 -->
            <mvc:mapping path="/StudentHandler/**"/>
            <bean class="net.fuzui.StudentInfo.interceptor.StudentLoginInterceptor"></bean>
        </mvc:interceptor>
        <mvc:interceptor>
        	<!-- 学生登录拦截 -->
            <!-- 拦截所有URL中包含/StudentHandler/的请求 -->
            <mvc:mapping path="/student/**"/>
            <bean class="net.fuzui.StudentInfo.interceptor.StudentLoginInterceptor"></bean>
        </mvc:interceptor>
        <mvc:interceptor>
        	<!-- 教师登录拦截 -->
            <!-- 拦截所有URL中包含CourseHandler/的请求 -->
            <mvc:mapping path="/CoursePlanHandler/**"/>
            <bean class="net.fuzui.StudentInfo.interceptor.TeacherLoginInterceptor"></bean>
        </mvc:interceptor>
        <mvc:interceptor>
        	<!-- 教师登录拦截 -->
            <!-- 拦截所有URL中包含/StudentHandler/的请求 -->
            <mvc:mapping path="/TeacherHandler/**"/>
            <bean class="net.fuzui.StudentInfo.interceptor.TeacherLoginInterceptor"></bean>
        </mvc:interceptor>
        <mvc:interceptor>
        	<!-- 教师登录拦截 -->
            <!-- 拦截所有URL中包含/StudentHandler/的请求 -->
            <mvc:mapping path="/teacher/**"/>
            <bean class="net.fuzui.StudentInfo.interceptor.TeacherLoginInterceptor"></bean>
        </mvc:interceptor>
    </mvc:interceptors>

</beans>