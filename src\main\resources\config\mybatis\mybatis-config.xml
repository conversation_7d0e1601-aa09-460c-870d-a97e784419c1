<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>

    <typeHandlers>
        <!-- handler属性直接配置我们要指定的TypeHandler -->
        <typeHandler handler="net.fuzui.StudentInfo.handler.MyListTypeHandler"/>
    </typeHandlers>

    <plugins>

        <plugin interceptor="com.github.pagehelper.PageInterceptor">

            <!--分页参数合理化 -->
            <property name="reasonable" value="true"/>

        </plugin>

    </plugins>

</configuration>