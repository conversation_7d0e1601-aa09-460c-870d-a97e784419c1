<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="net.fuzui.StudentInfo.mapper.CoursePlanMapper">

	<!-- 为了返回List，类型而定义的resultMap -->
	<resultMap type="CoursePlan" id="resultListCoursePlan">
		<result column="courseclass" property="courseclass" />
		<result column="coursetime" property="coursetime" />
		<result column="courseweek" property="courseweek" />
		<result column="cid" property="cid" />
		<result column="tid" property="tid" />
		<result column="classroom" property="classroom" />
		<result column="credits" property="credits" />
		<result column="period" property="period" />
		<result column="totalnum" property="totalnum" />

	</resultMap>
	
	<resultMap type="CourseGrade" id="resultListCourseGrade">
		<result column="sid" property="sid" />
		<result column="sname" property="sname" />
		<result column="grade" property="grade" />
	

	</resultMap>

	<!-- 添加课程安排信息 -->
	<insert id="insertCoursePlan" parameterType="CoursePlan">
		insert into courseplan(courseclass,coursetime,courseweek,cid,tid,classroom,credits,period,totalnum)
		value (#{courseclass}, #{coursetime},#{courseweek},#{cid},#{tid},#{classroom},#{credits},#{period},#{totalnum})
	</insert>

	<!-- 根据开课班级名删除课程安排信息 -->
	<delete id="deleteCoursePlan" parameterType="String">
		delete from courseplan where courseclass=#{courseclass}
	</delete>

	<!-- 根据开课班级名修改课程安排信息 -->
	<update id="modifyCoursePlan" parameterType="CoursePlan">
		update courseplan set
		coursetime=#{coursetime},courseweek=#{courseweek},cid=#{cid},tid=#{tid},classroom=#{classroom},credits=#{credits},period=#{period},totalnum=#{totalnum}
		where courseclass =#{courseclass}
	</update>

	<!-- 根据排课班级名查询课程安排信息 -->
	<select id="getByCoursePlanCourseclass" parameterType="map" resultMap="resultListCoursePlan">
		select * from courseplan where courseclass = #{courseclass} <!-- limit #{pageNo} , #{pageSize} -->
	</select>

	<!-- 根据教师id查询该教师所安排课程信息 -->
	<select id="getByCoursePlanTid" parameterType="map" resultMap="resultListCoursePlan">
		select * from courseplan where tid = #{tid} <!-- limit #{pageNo} , #{pageSize} -->
	</select>

	<!-- 根据教师id查询该教师所安排课程的课程编号，以方便查询课程具体信息 -->
	<select id="getCidByCoursePlanTid" parameterType="map" resultMap="resultListCoursePlan">
		select cid from courseplan where tid = #{tid} <!-- limit #{pageNo} , #{pageSize} -->
	</select>

	<!-- 根据课程id查询该课程的上课教师，以方便查询教师具体信息 -->
	<select id="getTidByCoursePlanCid" parameterType="map" resultMap="resultListCoursePlan">
		select * from courseplan where cid = #{cid} <!-- limit #{pageNo} , #{pageSize} -->
	</select>

	<!-- 根据上课时间、地点、查询课程安排信息，为了ajax查重 -->
	<select id="ajaxGetCoursePlan" resultType="java.lang.String">
		select * from courseplan where coursetime = #{coursetime} and courseweek=#{courseweek} and classroom=#{classroom}
	</select>

	<!-- 根据课程编号查询该课程，用于查询是否有有安排课程 -->
	<select id="existsCoursePlan" resultType="java.lang.String">
		select * from courseplan where cid = #{cid}
	</select>
	
	<!-- 根据课程编号查询该课程，用于查询是否有有安排课程 -->
	<select id="getCreditsByCid" resultType="java.lang.Integer">
		select credits from courseplan where cid = #{cid}
	</select>

	<!-- 根据课程编号查询该课程，用于查询是否有有安排课程 -->
	<select id="getCourseGrade" parameterType="map" resultMap="resultListCourseGrade">
		select g.sid,s.Sname,g.grade from grade g INNER JOIN student s on s.sid=g.sid where cid = #{cid}
	</select>
	
</mapper>