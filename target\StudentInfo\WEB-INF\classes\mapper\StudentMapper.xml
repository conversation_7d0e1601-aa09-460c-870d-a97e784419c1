<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="net.fuzui.StudentInfo.mapper.StudentMapper">
	<!-- 为了返回List，类型而定义的resultMap -->
	<resultMap type="Student" id="resultListStudent">
		<result column="sid" property="sid" />
		<result column="sname" property="sname" />
		<result column="sidcard" property="sidcard" />
		<result column="ssex" property="ssex" />
		<result column="spassword" property="spassword" />
		<result column="sage" property="sage" />
		<result column="classr" property="classr" />
		<result column="profession" property="profession" />
		<result column="college" property="college" />
	</resultMap>





	<!-- 添加学生信息 -->
	<insert id="insertStudent" parameterType="Student">
		insert into student(sid,sname,sidcard,ssex,spassword,sage,classr,profession,college)
		value (#{sid}, #{sname},#{sidcard},#{ssex},#{spassword},#{sage} ,#{classr}, #{profession}, #{college})
	</insert>

	<!-- 根据学号删除学生信息 -->
	<delete id="deleteStudent" parameterType="String">
		delete from student where sid=#{sid}
	</delete>

	<!-- 根据学号修改学生信息 -->
	<update id="modifyStudent" parameterType="Student">
		update student set
		sname=#{sname},sidcard=#{sidcard},ssex=#{ssex},spassword=#{spassword},sage=#{sage},classr=#{classr},profession=#{profession},college=#{college}
		where sid =#{sid}
	</update>

	<!-- 修改学生密码 -->
	<update id="modifyStudentPwd">
		update student set spassword=#{spassword} where sid =#{sid}
	</update>

	<!-- 根据学号查询出学生实体 -->
	<select id="getByStuSid" parameterType="String" resultType="net.fuzui.StudentInfo.pojo.Student">
		SELECT * from student where sid = #{sid}
	</select>

	<!-- 学生登录设置 -->
	<select id="queryByNamePwd" resultType="java.lang.String">
		select * from student where sid = #{sid} and spassword= #{spassword}
	</select>

	<!-- 查询全部学生 -->
	<select id="selectStudentBySql" parameterType="map" resultMap="resultListStudent">
		select * from student <!-- limit #{pageNo} , #{pageSize} -->
	</select>

	<!-- 根据学号查询学生信息 -->
	<select id="getByStudentSid" parameterType="map" resultMap="resultListStudent">
		select * from student where sid = #{sid} <!-- limit #{pageNo} , #{pageSize} -->
	</select>

	<!-- 根据学院查询学生信息 -->
	<select id="getByStudentCol" parameterType="map" resultMap="resultListStudent">
		select * from student where college = #{college} <!-- limit #{pageNo} , #{pageSize} -->
	</select>

	<!-- 根据专业查询学生信息 -->
	<select id="getByStudentPro" parameterType="map" resultMap="resultListStudent">
		select * from student where profession = #{profession} <!-- limit #{pageNo} , #{pageSize} -->
	</select>

	<!-- 根据班级查询学生信息 -->
	<select id="getByStudentCla" parameterType="map" resultMap="resultListStudent">
		select * from student where classr = #{classr} <!-- limit #{pageNo} , #{pageSize} -->
	</select>

	<!-- ajax验证学号是否存在 -->
	<select id="ajaxQueryBySid" resultType="java.lang.String">
		select sid from student where sid = #{sid}
	</select>

</mapper>