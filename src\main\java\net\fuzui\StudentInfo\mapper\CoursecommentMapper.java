package net.fuzui.StudentInfo.mapper;

import net.fuzui.StudentInfo.pojo.Course;
import net.fuzui.StudentInfo.pojo.Coursecomment;
import net.fuzui.StudentInfo.pojo.CoursecommentCust;

import java.util.List;

/**
 * @description: description
 * @author: tan<PERSON><PERSON><PERSON><PERSON>
 * @create: 2021-02-09 16:11
 */
public interface CoursecommentMapper {

    /**
     *  新增
     */
    public int insert(Coursecomment course);

    /**
     *  删除
     */
    public int delete(String ccid);

    /**
     * 查询
     * @param cid
     * @return
     */
    List<CoursecommentCust> query(String cid);
}
