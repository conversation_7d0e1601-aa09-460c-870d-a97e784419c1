<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.fuzui.StudentInfo.mapper.CoursecommentMapper">
    <!-- 为了返回List，类型而定义的resultMap -->
    <resultMap type="Coursecomment" id="resultListCoursecomment">
        <result column="ccid" property="ccid" />
        <result column="cid" property="cid" />
        <result column="sid" property="sid" />
        <result column="content" property="content" />
        <result column="score" property="score" />
        <result column="ctime" property="ctime" />
    </resultMap>
    <!-- 添加 -->
    <insert id="insert" parameterType="Coursecomment">
        INSERT INTO `coursecomment`
            (`ccid`, `cid`, `sid`, `content`, `score`)
        VALUES (#{ccid}, #{cid}, #{sid}, #{content}, #{score})
    </insert>
    <!-- 删除 -->
    <delete id="delete" parameterType="String">
        delete from coursecomment where ccid=#{ccid}
    </delete>

    <select id="query" resultType="net.fuzui.StudentInfo.pojo.CoursecommentCust">
        SELECT
            c.Cname as cname,s.Sname as sname,
            cc.*
        FROM course c
                 INNER JOIN coursecomment cc on(c.Cid=cc.cid)
                 INNER JOIN student s on (cc.sid=s.Sid)
        WHERE c.Cid=#{cid}
        order by cc.ctime desc
    </select>

</mapper>